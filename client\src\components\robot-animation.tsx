import React, { useEffect, useRef, useState } from 'react';

interface RobotAnimationProps {
  width?: number;
  height?: number;
  className?: string;
  animationState?: 'idle' | 'talking' | 'listening' | 'thinking' | 'dancing';
  onAnimationStateChange?: (state: string) => void;
  showControls?: boolean;
}

const RobotAnimation: React.FC<RobotAnimationProps> = ({
  width = 400,
  height = 400,
  className = '',
  animationState = 'idle',
  onAnimationStateChange,
  showControls = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const [currentState, setCurrentState] = useState(animationState);
  const [scale, setScale] = useState(0.5);

  // Robot properties
  const robotRef = useRef({
    x: 0,
    y: 0,
    bodyRotation: 0,
    headRotation: 0,
    eyeScale: 1,
    armRotation: 0,
    legOffset: 0,
    time: 0,
    speaking: false,
    mouthScale: 1
  });

  useEffect(() => {
    setCurrentState(animationState);
  }, [animationState]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = width;
    canvas.height = height;

    const robot = robotRef.current;
    robot.x = width / 2;
    robot.y = height / 2;

    const animate = () => {
      ctx.clearRect(0, 0, width, height);
      
      robot.time += 0.05;

      // Update animation based on current state
      switch (currentState) {
        case 'idle':
          robot.bodyRotation = Math.sin(robot.time * 0.5) * 0.05;
          robot.headRotation = Math.sin(robot.time * 0.3) * 0.1;
          robot.eyeScale = 1 + Math.sin(robot.time * 2) * 0.1;
          robot.armRotation = Math.sin(robot.time * 0.4) * 0.2;
          robot.mouthScale = 1;
          break;
        
        case 'talking':
          robot.bodyRotation = Math.sin(robot.time * 0.8) * 0.08;
          robot.headRotation = Math.sin(robot.time * 0.6) * 0.15;
          robot.eyeScale = 1 + Math.sin(robot.time * 3) * 0.2;
          robot.armRotation = Math.sin(robot.time * 0.7) * 0.3;
          robot.mouthScale = 1 + Math.sin(robot.time * 8) * 0.3;
          break;
        
        case 'listening':
          robot.bodyRotation = 0;
          robot.headRotation = Math.sin(robot.time * 0.2) * 0.05;
          robot.eyeScale = 1.2;
          robot.armRotation = 0;
          robot.mouthScale = 1;
          break;
        
        case 'thinking':
          robot.bodyRotation = Math.sin(robot.time * 0.3) * 0.03;
          robot.headRotation = Math.sin(robot.time * 0.4) * 0.2;
          robot.eyeScale = 0.8 + Math.sin(robot.time * 1.5) * 0.2;
          robot.armRotation = Math.sin(robot.time * 0.5) * 0.1;
          robot.mouthScale = 1;
          break;
        
        case 'dancing':
          robot.bodyRotation = Math.sin(robot.time * 2) * 0.3;
          robot.headRotation = Math.sin(robot.time * 1.5) * 0.4;
          robot.eyeScale = 1 + Math.sin(robot.time * 4) * 0.3;
          robot.armRotation = Math.sin(robot.time * 3) * 0.8;
          robot.legOffset = Math.sin(robot.time * 2.5) * 10;
          robot.mouthScale = 1.5;
          break;
      }

      // Draw robot
      drawRobot(ctx, robot, scale);

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [width, height, currentState, scale]);

  const drawRobot = (ctx: CanvasRenderingContext2D, robot: any, scale: number) => {
    ctx.save();
    ctx.translate(robot.x, robot.y);
    ctx.scale(scale, scale);
    ctx.rotate(robot.bodyRotation);

    // Body
    ctx.fillStyle = '#4A90E2';
    ctx.fillRect(-40, -30, 80, 60);
    
    // Head
    ctx.save();
    ctx.rotate(robot.headRotation);
    ctx.fillStyle = '#5BA0F2';
    ctx.fillRect(-30, -60, 60, 40);
    
    // Eyes
    ctx.save();
    ctx.scale(robot.eyeScale, robot.eyeScale);
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(-20, -50, 15, 10);
    ctx.fillRect(5, -50, 15, 10);
    ctx.fillStyle = '#000000';
    ctx.fillRect(-15, -47, 5, 4);
    ctx.fillRect(10, -47, 5, 4);
    ctx.restore();
    
    // Mouth
    ctx.save();
    ctx.scale(robot.mouthScale, robot.mouthScale);
    ctx.fillStyle = '#333333';
    ctx.fillRect(-10, -35, 20, 5);
    ctx.restore();
    
    ctx.restore();

    // Arms
    ctx.save();
    ctx.rotate(robot.armRotation);
    ctx.fillStyle = '#4A90E2';
    ctx.fillRect(-60, -20, 20, 40);
    ctx.fillRect(40, -20, 20, 40);
    ctx.restore();

    // Legs
    ctx.fillStyle = '#4A90E2';
    ctx.fillRect(-30, 30 + robot.legOffset, 20, 40);
    ctx.fillRect(10, 30 - robot.legOffset, 20, 40);

    ctx.restore();
  };

  const handleStateChange = (newState: string) => {
    setCurrentState(newState as any);
    onAnimationStateChange?.(newState);
  };

  const handleScaleChange = (newScale: number) => {
    setScale(Math.max(0.2, Math.min(1.5, newScale)));
  };

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <canvas
        ref={canvasRef}
        className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
        style={{ width: `${width}px`, height: `${height}px` }}
      />
      
      {showControls && (
        <div className="mt-4 space-y-4">
          {/* Animation Controls */}
          <div className="flex flex-wrap gap-2 justify-center">
            {['idle', 'talking', 'listening', 'thinking', 'dancing'].map((state) => (
              <button
                key={state}
                onClick={() => handleStateChange(state)}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  currentState === state
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                {state.charAt(0).toUpperCase() + state.slice(1)}
              </button>
            ))}
          </div>
          
          {/* Scale Control */}
          <div className="flex items-center gap-2 justify-center">
            <button
              onClick={() => handleScaleChange(scale - 0.1)}
              className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-sm"
            >
              −
            </button>
            <span className="text-sm min-w-[60px] text-center">
              {Math.round(scale * 100)}%
            </span>
            <button
              onClick={() => handleScaleChange(scale + 0.1)}
              className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-sm"
            >
              +
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RobotAnimation;
