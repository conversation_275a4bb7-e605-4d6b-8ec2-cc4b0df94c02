import React, { useEffect, useRef, useState } from 'react';

interface RobotAnimationProps {
  width?: number;
  height?: number;
  className?: string;
  animationState?: 'idle' | 'talking' | 'listening' | 'thinking' | 'dancing';
  onAnimationStateChange?: (state: string) => void;
  showControls?: boolean;
}

const RobotAnimation: React.FC<RobotAnimationProps> = ({
  width = 400,
  height = 400,
  className = '',
  animationState = 'idle',
  onAnimationStateChange,
  showControls = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const [currentState, setCurrentState] = useState(animationState);
  const [scale, setScale] = useState(0.5);

  // Robot properties
  const robotRef = useRef({
    x: 0,
    y: 0,
    bodyRotation: 0,
    headRotation: 0,
    eyeScale: 1,
    armRotation: 0,
    legOffset: 0,
    time: 0,
    speaking: false,
    mouthScale: 1
  });

  // Polyfill for roundRect if not available
  const drawRoundRect = (ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius: number) => {
    if (typeof (ctx as any).roundRect === 'function') {
      (ctx as any).roundRect(x, y, width, height, radius);
    } else {
      // Manual rounded rectangle
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
    }
  };

  useEffect(() => {
    setCurrentState(animationState);
  }, [animationState]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = width;
    canvas.height = height;

    const robot = robotRef.current;
    robot.x = width / 2;
    robot.y = height / 2;

    const animate = () => {
      ctx.clearRect(0, 0, width, height);

      robot.time += 0.05;

      // Update animation based on current state
      switch (currentState) {
        case 'idle':
          robot.bodyRotation = Math.sin(robot.time * 0.5) * 0.05;
          robot.headRotation = Math.sin(robot.time * 0.3) * 0.1;
          robot.eyeScale = 1 + Math.sin(robot.time * 2) * 0.1;
          robot.armRotation = Math.sin(robot.time * 0.4) * 0.2;
          robot.mouthScale = 1;
          robot.speaking = false;
          break;

        case 'talking':
          robot.bodyRotation = Math.sin(robot.time * 0.8) * 0.08;
          robot.headRotation = Math.sin(robot.time * 0.6) * 0.15;
          robot.eyeScale = 1 + Math.sin(robot.time * 3) * 0.2;
          robot.armRotation = Math.sin(robot.time * 0.7) * 0.3;
          robot.mouthScale = 1 + Math.sin(robot.time * 8) * 0.3;
          robot.speaking = true;
          break;

        case 'listening':
          robot.bodyRotation = 0;
          robot.headRotation = Math.sin(robot.time * 0.2) * 0.05;
          robot.eyeScale = 1.2;
          robot.armRotation = 0;
          robot.mouthScale = 1;
          robot.speaking = false;
          break;

        case 'thinking':
          robot.bodyRotation = Math.sin(robot.time * 0.3) * 0.03;
          robot.headRotation = Math.sin(robot.time * 0.4) * 0.2;
          robot.eyeScale = 0.8 + Math.sin(robot.time * 1.5) * 0.2;
          robot.armRotation = Math.sin(robot.time * 0.5) * 0.1;
          robot.mouthScale = 1;
          robot.speaking = false;
          break;

        case 'dancing':
          robot.bodyRotation = Math.sin(robot.time * 2) * 0.3;
          robot.headRotation = Math.sin(robot.time * 1.5) * 0.4;
          robot.eyeScale = 1 + Math.sin(robot.time * 4) * 0.3;
          robot.armRotation = Math.sin(robot.time * 3) * 0.8;
          robot.legOffset = Math.sin(robot.time * 2.5) * 10;
          robot.mouthScale = 1.5;
          robot.speaking = false;
          break;
      }

      // Draw robot
      drawRobot(ctx, robot, scale);

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [width, height, currentState, scale]);

  const drawRobot = (ctx: CanvasRenderingContext2D, robot: any, scale: number) => {
    ctx.save();
    ctx.translate(robot.x, robot.y);
    ctx.scale(scale, scale);
    ctx.rotate(robot.bodyRotation);

    // Shadow
    ctx.save();
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.beginPath();
    ctx.ellipse(0, 80, 60, 15, 0, 0, 2 * Math.PI);
    ctx.fill();
    ctx.restore();

    // Body (white/light gray sphere)
    ctx.save();
    const bodyGradient = ctx.createRadialGradient(-20, -20, 0, 0, 0, 50);
    bodyGradient.addColorStop(0, '#FFFFFF');
    bodyGradient.addColorStop(0.7, '#F0F0F0');
    bodyGradient.addColorStop(1, '#E0E0E0');
    ctx.fillStyle = bodyGradient;
    ctx.beginPath();
    ctx.arc(0, 10, 45, 0, 2 * Math.PI);
    ctx.fill();

    // Body highlight
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
    ctx.beginPath();
    ctx.ellipse(-15, -5, 20, 25, -0.3, 0, 2 * Math.PI);
    ctx.fill();
    ctx.restore();

    // Head (blue sphere)
    ctx.save();
    ctx.rotate(robot.headRotation);
    const headGradient = ctx.createRadialGradient(-15, -15, 0, 0, 0, 35);
    headGradient.addColorStop(0, '#6BB6FF');
    headGradient.addColorStop(0.7, '#4A90E2');
    headGradient.addColorStop(1, '#357ABD');
    ctx.fillStyle = headGradient;
    ctx.beginPath();
    ctx.arc(0, -50, 32, 0, 2 * Math.PI);
    ctx.fill();

    // Head highlight
    ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
    ctx.beginPath();
    ctx.ellipse(-10, -60, 12, 18, -0.3, 0, 2 * Math.PI);
    ctx.fill();

    // Visor/Eye area (dark horizontal band)
    ctx.save();
    ctx.scale(robot.eyeScale, robot.eyeScale);
    ctx.fillStyle = '#1a1a1a';
    ctx.beginPath();
    drawRoundRect(ctx, -25, -55, 50, 12, 6);
    ctx.fill();

    // Eyes (glowing dots)
    ctx.fillStyle = robot.speaking ? '#00FF88' : '#0088FF';
    ctx.shadowColor = robot.speaking ? '#00FF88' : '#0088FF';
    ctx.shadowBlur = 8;
    ctx.beginPath();
    ctx.arc(-12, -49, 3, 0, 2 * Math.PI);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(12, -49, 3, 0, 2 * Math.PI);
    ctx.fill();
    ctx.shadowBlur = 0;
    ctx.restore();

    ctx.restore();

    // Arms (black/dark gray)
    ctx.save();
    ctx.rotate(robot.armRotation);

    // Left arm
    const armGradient = ctx.createLinearGradient(-70, 0, -50, 0);
    armGradient.addColorStop(0, '#2a2a2a');
    armGradient.addColorStop(0.5, '#1a1a1a');
    armGradient.addColorStop(1, '#0a0a0a');
    ctx.fillStyle = armGradient;
    ctx.beginPath();
    drawRoundRect(ctx, -70, -5, 25, 35, 12);
    ctx.fill();

    // Right arm
    ctx.beginPath();
    drawRoundRect(ctx, 45, -5, 25, 35, 12);
    ctx.fill();

    // Arm joints
    ctx.fillStyle = '#333333';
    ctx.beginPath();
    ctx.arc(-57, 12, 8, 0, 2 * Math.PI);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(57, 12, 8, 0, 2 * Math.PI);
    ctx.fill();

    ctx.restore();

    // Legs (same color as body but smaller)
    const legGradient = ctx.createLinearGradient(0, 50, 0, 80);
    legGradient.addColorStop(0, '#F0F0F0');
    legGradient.addColorStop(1, '#D0D0D0');
    ctx.fillStyle = legGradient;

    // Left leg
    ctx.beginPath();
    drawRoundRect(ctx, -20, 50 + robot.legOffset, 15, 25, 7);
    ctx.fill();

    // Right leg
    ctx.beginPath();
    drawRoundRect(ctx, 5, 50 - robot.legOffset, 15, 25, 7);
    ctx.fill();

    // Feet
    ctx.fillStyle = '#1a1a1a';
    ctx.beginPath();
    drawRoundRect(ctx, -22, 72 + robot.legOffset, 19, 8, 4);
    ctx.fill();
    ctx.beginPath();
    drawRoundRect(ctx, 3, 72 - robot.legOffset, 19, 8, 4);
    ctx.fill();

    ctx.restore();
  };

  const handleStateChange = (newState: string) => {
    setCurrentState(newState as any);
    onAnimationStateChange?.(newState);
  };

  const handleScaleChange = (newScale: number) => {
    setScale(Math.max(0.2, Math.min(1.5, newScale)));
  };

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <canvas
        ref={canvasRef}
        className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
        style={{ width: `${width}px`, height: `${height}px` }}
      />

      {showControls && (
        <div className="mt-4 space-y-4">
          {/* Animation Controls */}
          <div className="flex flex-wrap gap-2 justify-center">
            {['idle', 'talking', 'listening', 'thinking', 'dancing'].map((state) => (
              <button
                key={state}
                onClick={() => handleStateChange(state)}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  currentState === state
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                {state.charAt(0).toUpperCase() + state.slice(1)}
              </button>
            ))}
          </div>

          {/* Scale Control */}
          <div className="flex items-center gap-2 justify-center">
            <button
              onClick={() => handleScaleChange(scale - 0.1)}
              className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-sm"
            >
              −
            </button>
            <span className="text-sm min-w-[60px] text-center">
              {Math.round(scale * 100)}%
            </span>
            <button
              onClick={() => handleScaleChange(scale + 0.1)}
              className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-sm"
            >
              +
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RobotAnimation;
