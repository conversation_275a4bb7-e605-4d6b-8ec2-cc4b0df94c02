import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Bot, Loader2, Send, Mic, MicOff, Volume2, VolumeX } from 'lucide-react';
import RobotAnimation from '@/components/robot-animation';
import { useToast } from '@/hooks/use-toast';

interface Message {
  id: string;
  role: 'user' | 'dasbot';
  content: string;
  timestamp: Date;
}

const Dasbot: React.FC = () => {
  const [, setLocation] = useLocation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [robotState, setRobotState] = useState<'idle' | 'talking' | 'listening' | 'thinking' | 'dancing'>('idle');
  const [isListening, setIsListening] = useState(false);
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(true);
  const inputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Auto return to idle after dancing
  useEffect(() => {
    if (robotState === 'dancing') {
      const timer = setTimeout(() => {
        setRobotState('idle');
      }, 5000); // Dance for 5 seconds then return to idle

      return () => clearTimeout(timer);
    }
  }, [robotState]);

  // Text-to-speech function
  const speakText = (text: string) => {
    if (!isSpeechEnabled || !('speechSynthesis' in window)) return;

    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 1.0;
    utterance.pitch = 1.0;
    utterance.volume = 1.0;

    utterance.onstart = () => setRobotState('talking');
    utterance.onend = () => setRobotState('idle');

    window.speechSynthesis.speak(utterance);
  };

  // Handle sending messages
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setRobotState('thinking');

    try {
      const response = await simulateDasbotResponse(userMessage.content);

      const botMessage: Message = {
        id: `dasbot-${Date.now()}`,
        role: 'dasbot',
        content: response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);

      if (isSpeechEnabled) {
        setTimeout(() => {
          speakText(response);
        }, 300);
      } else {
        setRobotState('idle');
      }

    } catch (error) {
      console.error('Error getting Dasbot response:', error);
      toast({
        title: "Error",
        description: "Sorry, I'm having trouble responding right now. Please try again.",
        variant: "destructive",
      });
      setRobotState('idle');
    } finally {
      setIsLoading(false);
    }
  };

  // Simulate Dasbot responses
  const simulateDasbotResponse = async (userInput: string): Promise<string> => {
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const input = userInput.toLowerCase();

    if (input.includes('hello') || input.includes('hi')) {
      return "Hello there! Great to see you! How can I help you today?";
    }

    if (input.includes('shop') || input.includes('buy') || input.includes('purchase')) {
      return "I'd love to help you shop! What are you looking for? I can help you find products, compare prices, and even help you make purchases with your Daswos Coins.";
    }

    if (input.includes('help')) {
      return "I'm here to help! I can assist you with shopping, answer questions about Daswos, help you navigate the platform, and much more. What specific help do you need?";
    }

    if (input.includes('autoshop') || input.includes('automatic')) {
      return "AutoShop is one of my favorite features! I can help you set up automated shopping based on your preferences and budget. Would you like me to help you configure AutoShop?";
    }

    if (input.includes('coin') || input.includes('daswos coin')) {
      return "Daswos Coins are our platform currency! You can use them to make purchases, and I can help you manage your coin balance. Would you like to check your current balance or learn how to earn more coins?";
    }

    if (input.includes('dance') || input.includes('move') || input.includes('spin')) {
      setTimeout(() => setRobotState('dancing'), 500);
      return "Let me show you my moves! 🕺 I love to dance and move around! Dancing makes shopping more fun, don't you think?";
    }

    if (input.includes('fun') || input.includes('play') || input.includes('game')) {
      setTimeout(() => setRobotState('dancing'), 500);
      return "I love having fun! Let me dance for you! 🎉 Shopping should always be enjoyable and exciting!";
    }

    if (input.includes('amazing') || input.includes('awesome') || input.includes('great')) {
      setTimeout(() => setRobotState('dancing'), 300);
      return "Thank you! That makes me so happy I could dance! 💃 I'm here to make your Daswos experience amazing!";
    }

    return "That's interesting! I'm always learning and improving. While I process your request, is there anything specific about Daswos I can help you with? I'm great at helping with shopping, product recommendations, and platform navigation!";
  };

  // Voice recognition toggle
  const toggleListening = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      toast({
        title: "Voice not supported",
        description: "Your browser doesn't support voice recognition.",
        variant: "destructive",
      });
      return;
    }

    if (isListening) {
      setIsListening(false);
      setRobotState('idle');
    } else {
      setIsListening(true);
      setRobotState('listening');
      toast({
        title: "Voice feature coming soon!",
        description: "Voice recognition will be available in the next update.",
      });
      setTimeout(() => {
        setIsListening(false);
        setRobotState('idle');
      }, 3000);
    }
  };

  return (
    <div className="min-h-screen bg-gray-200 dark:bg-gray-800 relative overflow-hidden">
      {/* Header */}
      <div className="bg-gray-200/90 dark:bg-gray-800/90 backdrop-blur-sm border-b border-gray-300 dark:border-gray-700 sticky top-0 z-20">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLocation('/')}
                className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Talk with Dasbot</h1>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setRobotState('dancing')}
                className="text-purple-600 dark:text-purple-300 hover:bg-purple-100 dark:hover:bg-purple-900"
                title="Make Dasbot dance!"
              >
                🕺
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setRobotState('thinking')}
                className="text-blue-600 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900"
                title="Make Dasbot think"
              >
                🤔
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsSpeechEnabled(!isSpeechEnabled)}
                className={`${isSpeechEnabled ? 'bg-green-100 text-green-700 border-green-300' : 'text-gray-600 dark:text-gray-300'}`}
              >
                {isSpeechEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Full Screen Robot */}
      <div className="absolute inset-0 top-20 flex items-center justify-center pointer-events-none">
        <RobotAnimation
          animationState={robotState}
          onAnimationStateChange={setRobotState}
          width={600}
          height={500}
        />
      </div>

      {/* Speech Bubble */}
      {messages.length > 0 && (
        <div className="absolute top-32 left-8 right-8 md:left-16 md:right-auto md:max-w-lg z-10">
          <div className="bg-gray-100 dark:bg-gray-700 rounded-3xl shadow-2xl p-6 relative">
            <div className="absolute bottom-0 left-12 transform translate-y-full">
              <div className="w-0 h-0 border-l-[20px] border-l-transparent border-r-[20px] border-r-transparent border-t-[20px] border-t-gray-100 dark:border-t-gray-700"></div>
            </div>

            <div className="space-y-3 max-h-60 overflow-y-auto">
              {messages.slice(-3).map((message) => (
                <div key={message.id} className={`${message.role === 'user' ? 'text-right' : 'text-left'}`}>
                  <div className={`inline-block px-4 py-2 rounded-2xl max-w-full ${
                    message.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                  }`}>
                    <p className="text-sm">{message.content}</p>
                  </div>
                </div>
              ))}

              {isLoading && (
                <div className="text-left">
                  <div className="inline-block bg-gray-100 dark:bg-gray-700 px-4 py-2 rounded-2xl">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Welcome Message */}
      {messages.length === 0 && (
        <div className="absolute top-32 left-8 right-8 md:left-16 md:right-auto md:max-w-lg z-10">
          <div className="bg-gray-100 dark:bg-gray-700 rounded-3xl shadow-2xl p-6 relative">
            <div className="absolute bottom-0 left-12 transform translate-y-full">
              <div className="w-0 h-0 border-l-[20px] border-l-transparent border-r-[20px] border-r-transparent border-t-[20px] border-t-gray-100 dark:border-t-gray-700"></div>
            </div>

            <div className="text-center">
              <Bot className="h-8 w-8 mx-auto mb-3 text-blue-500" />
              <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">Hi! I'm Dasbot</p>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">Your AI shopping assistant. How can I help you today?</p>

              <div className="space-y-2">
                <p className="text-xs text-gray-500 dark:text-gray-400">Try asking me about:</p>
                <div className="flex flex-wrap gap-2 justify-center">
                  <button
                    onClick={() => setInputMessage("Help me shop for something")}
                    className="px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                  >
                    Shopping
                  </button>
                  <button
                    onClick={() => setInputMessage("What are Daswos Coins?")}
                    className="px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                  >
                    Daswos Coins
                  </button>
                  <button
                    onClick={() => setInputMessage("Tell me about AutoShop")}
                    className="px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                  >
                    AutoShop
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Input Area - Fixed at bottom */}
      <div className="fixed bottom-0 left-0 right-0 z-20 bg-gray-200/90 dark:bg-gray-800/90 backdrop-blur-sm border-t border-gray-300 dark:border-gray-700">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <form onSubmit={handleSendMessage} className="flex space-x-2">
            <Input
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Type your message to Dasbot..."
              disabled={isLoading}
              className="flex-1 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600"
            />
            <Button
              type="button"
              variant="outline"
              onClick={toggleListening}
              disabled={isLoading}
              className={`text-gray-600 dark:text-gray-300 ${isListening ? 'bg-red-100 dark:bg-red-900' : ''}`}
            >
              {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !inputMessage.trim()}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Dasbot;
