import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'wouter';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Send, Mic, MicOff, Volume2, VolumeX } from 'lucide-react';
import RobotAnimation from '@/components/robot-animation';
import { useToast } from '@/hooks/use-toast';

interface Message {
  id: string;
  role: 'user' | 'dasbot';
  content: string;
  timestamp: Date;
}

const Dasbot: React.FC = () => {
  const [, setLocation] = useLocation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [robotState, setRobotState] = useState<'idle' | 'talking' | 'listening' | 'thinking'>('idle');
  const [isListening, setIsListening] = useState(false);
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage: Message = {
      id: 'welcome',
      role: 'dasbot',
      content: "Hello! I'm Dasbot, your friendly Daswos assistant. I'm here to help you with shopping, answer questions, and make your experience on Daswos amazing. What can I help you with today?",
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
    
    // Speak welcome message if speech is enabled
    if (isSpeechEnabled) {
      setTimeout(() => {
        speakText(welcomeMessage.content);
      }, 500);
    }
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Text-to-speech function
  const speakText = (text: string) => {
    if (!isSpeechEnabled || !('speechSynthesis' in window)) return;
    
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 1.0;
    utterance.pitch = 1.0;
    utterance.volume = 1.0;
    
    const voices = window.speechSynthesis.getVoices();
    const preferredVoice = voices.find(voice =>
      voice.name.includes('Google') || voice.name.includes('Natural') || voice.name.includes('Samantha')
    );
    
    if (preferredVoice) {
      utterance.voice = preferredVoice;
    }
    
    utterance.onstart = () => setRobotState('talking');
    utterance.onend = () => setRobotState('idle');
    
    window.speechSynthesis.speak(utterance);
  };

  // Handle sending messages
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setRobotState('thinking');

    try {
      // Simulate API call to Dasbot service
      // In a real implementation, this would call your AI service
      const response = await simulateDasbotResponse(userMessage.content);
      
      const botMessage: Message = {
        id: `dasbot-${Date.now()}`,
        role: 'dasbot',
        content: response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
      
      // Speak the response
      if (isSpeechEnabled) {
        setTimeout(() => {
          speakText(response);
        }, 300);
      } else {
        setRobotState('idle');
      }

    } catch (error) {
      console.error('Error getting Dasbot response:', error);
      toast({
        title: "Error",
        description: "Sorry, I'm having trouble responding right now. Please try again.",
        variant: "destructive",
      });
      setRobotState('idle');
    } finally {
      setIsLoading(false);
    }
  };

  // Simulate Dasbot responses (replace with actual AI service)
  const simulateDasbotResponse = async (userInput: string): Promise<string> => {
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const input = userInput.toLowerCase();
    
    if (input.includes('hello') || input.includes('hi')) {
      return "Hello there! Great to see you! How can I help you today?";
    }
    
    if (input.includes('shop') || input.includes('buy') || input.includes('purchase')) {
      return "I'd love to help you shop! What are you looking for? I can help you find products, compare prices, and even help you make purchases with your Daswos Coins.";
    }
    
    if (input.includes('help')) {
      return "I'm here to help! I can assist you with shopping, answer questions about Daswos, help you navigate the platform, and much more. What specific help do you need?";
    }
    
    if (input.includes('autoshop') || input.includes('automatic')) {
      return "AutoShop is one of my favorite features! I can help you set up automated shopping based on your preferences and budget. Would you like me to help you configure AutoShop?";
    }
    
    if (input.includes('coin') || input.includes('daswos coin')) {
      return "Daswos Coins are our platform currency! You can use them to make purchases, and I can help you manage your coin balance. Would you like to check your current balance or learn how to earn more coins?";
    }
    
    return "That's interesting! I'm always learning and improving. While I process your request, is there anything specific about Daswos I can help you with? I'm great at helping with shopping, product recommendations, and platform navigation!";
  };

  // Voice recognition (simplified - you might want to use the existing voice hooks)
  const toggleListening = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      toast({
        title: "Voice not supported",
        description: "Your browser doesn't support voice recognition.",
        variant: "destructive",
      });
      return;
    }

    if (isListening) {
      setIsListening(false);
      setRobotState('idle');
    } else {
      setIsListening(true);
      setRobotState('listening');
      // Implement voice recognition here
      toast({
        title: "Voice feature coming soon!",
        description: "Voice recognition will be available in the next update.",
      });
      setTimeout(() => {
        setIsListening(false);
        setRobotState('idle');
      }, 3000);
    }
  };

  return (
    <div className="min-h-screen bg-[#E0E0E0] dark:bg-[#222222] p-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setLocation('/')}
              className="bg-white dark:bg-gray-800"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Talk with Dasbot
            </h1>
          </div>
          
          <Button
            variant="outline"
            size="icon"
            onClick={() => setIsSpeechEnabled(!isSpeechEnabled)}
            className="bg-white dark:bg-gray-800"
          >
            {isSpeechEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Robot Animation */}
          <div className="lg:col-span-1">
            <Card className="bg-white dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="text-center">Dasbot</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-center">
                <RobotAnimation
                  width={300}
                  height={300}
                  animationState={robotState}
                  className="w-full"
                />
              </CardContent>
            </Card>
          </div>

          {/* Chat Interface */}
          <div className="lg:col-span-2">
            <Card className="bg-white dark:bg-gray-800 h-[600px] flex flex-col">
              <CardHeader>
                <CardTitle>Chat with Dasbot</CardTitle>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col p-0">
                {/* Messages */}
                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[80%] p-3 rounded-lg ${
                            message.role === 'user'
                              ? 'bg-blue-500 text-white'
                              : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                          }`}
                        >
                          <p className="text-sm">{message.content}</p>
                          <p className="text-xs opacity-70 mt-1">
                            {message.timestamp.toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    ))}
                    
                    {isLoading && (
                      <div className="flex justify-start">
                        <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  <div ref={messagesEndRef} />
                </ScrollArea>

                <Separator />

                {/* Input */}
                <div className="p-4">
                  <form onSubmit={handleSendMessage} className="flex gap-2">
                    <Input
                      ref={inputRef}
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      placeholder="Type your message to Dasbot..."
                      disabled={isLoading}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={toggleListening}
                      disabled={isLoading}
                      className={isListening ? 'bg-red-100 dark:bg-red-900' : ''}
                    >
                      {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                    </Button>
                    <Button type="submit" disabled={!inputMessage.trim() || isLoading}>
                      <Send className="h-4 w-4" />
                    </Button>
                  </form>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dasbot;
