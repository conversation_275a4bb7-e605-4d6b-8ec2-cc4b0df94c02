// Mock API service for development when backend is not available
// This provides fallback functionality for cart and other API operations

interface MockCartItem {
  id: number;
  productId: string;
  quantity: number;
  price: number;
  source: string;
  name: string;
  product?: {
    title: string;
    price: number;
    imageUrl?: string;
    images?: string[];
  };
}

class MockApiService {
  private cartItems: MockCartItem[] = [];
  private nextId = 1;

  // Initialize with some sample data
  constructor() {
    this.initializeSampleData();
  }

  private initializeSampleData() {
    // Add some sample cart items for demonstration
    this.cartItems = [
      {
        id: this.nextId++,
        productId: 'sample-1',
        quantity: 2,
        price: 29.99,
        source: 'manual',
        name: 'Sample Product 1',
        product: {
          title: 'Sample Product 1',
          price: 29.99,
          imageUrl: '/placeholder-product.png',
          images: ['/placeholder-product.png']
        }
      },
      {
        id: this.nextId++,
        productId: 'sample-2',
        quantity: 1,
        price: 15.50,
        source: 'ai_shopper',
        name: 'AI Recommended Item',
        product: {
          title: 'AI Recommended Item',
          price: 15.50,
          imageUrl: '/placeholder-product.png',
          images: ['/placeholder-product.png']
        }
      }
    ];
  }

  // Mock cart operations
  async getCartItems(): Promise<MockCartItem[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));
    return [...this.cartItems];
  }

  async addToCart(productId: string, quantity: number = 1, source: string = 'manual'): Promise<MockCartItem> {
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Check if item already exists
    const existingItem = this.cartItems.find(item => item.productId === productId);
    
    if (existingItem) {
      existingItem.quantity += quantity;
      return existingItem;
    }
    
    // Create new item
    const newItem: MockCartItem = {
      id: this.nextId++,
      productId,
      quantity,
      price: Math.random() * 50 + 10, // Random price between $10-60
      source,
      name: `Product ${productId}`,
      product: {
        title: `Product ${productId}`,
        price: Math.random() * 50 + 10,
        imageUrl: '/placeholder-product.png',
        images: ['/placeholder-product.png']
      }
    };
    
    this.cartItems.push(newItem);
    return newItem;
  }

  async updateCartItem(itemId: number, quantity: number): Promise<MockCartItem | null> {
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const item = this.cartItems.find(item => item.id === itemId);
    if (item) {
      item.quantity = quantity;
      return item;
    }
    return null;
  }

  async removeFromCart(itemId: number): Promise<boolean> {
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const index = this.cartItems.findIndex(item => item.id === itemId);
    if (index !== -1) {
      this.cartItems.splice(index, 1);
      return true;
    }
    return false;
  }

  async clearCart(): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 100));
    this.cartItems = [];
  }

  // Mock user operations
  async getCurrentUser(): Promise<any> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return {
      id: 'demo-user',
      username: 'demo',
      email: '<EMAIL>',
      isAuthenticated: true
    };
  }

  // Mock AI operations
  async getAiShopperStatus(): Promise<any> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return {
      enabled: false,
      settings: {
        autoPurchase: false,
        budgetLimit: 100,
        preferredCategories: [],
        avoidTags: [],
        minimumTrustScore: 80
      }
    };
  }

  // Mock AutoShop operations
  async getAutoShopPending(): Promise<any[]> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return [];
  }

  async getAutoShopHistory(): Promise<any[]> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return [];
  }

  // Check if we should use mock API (when real API is not available)
  static shouldUseMockApi(): boolean {
    // In development, if we detect that API calls are failing, use mock
    return process.env.NODE_ENV === 'development' || window.location.port === '3003';
  }
}

// Create singleton instance
export const mockApiService = new MockApiService();

// Enhanced API request function that falls back to mock when needed
export async function enhancedApiRequest(url: string, options: RequestInit = {}): Promise<any> {
  try {
    // Try real API first
    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.warn(`Real API failed for ${url}, using mock data:`, error);
    
    // Fall back to mock API for specific endpoints
    if (url.includes('/api/user/cart')) {
      if (options.method === 'GET' || !options.method) {
        return await mockApiService.getCartItems();
      } else if (options.method === 'POST' && url.includes('/add')) {
        const body = JSON.parse(options.body as string);
        return await mockApiService.addToCart(body.productId, body.quantity, body.source);
      } else if (options.method === 'PUT') {
        const itemId = parseInt(url.split('/').pop() || '0');
        const body = JSON.parse(options.body as string);
        return await mockApiService.updateCartItem(itemId, body.quantity);
      } else if (options.method === 'DELETE') {
        const itemId = parseInt(url.split('/').pop() || '0');
        return await mockApiService.removeFromCart(itemId);
      }
    } else if (url.includes('/api/user') && !url.includes('/cart')) {
      return await mockApiService.getCurrentUser();
    } else if (url.includes('/api/user/ai-shopper')) {
      return await mockApiService.getAiShopperStatus();
    } else if (url.includes('/api/user/autoshop/pending')) {
      return await mockApiService.getAutoShopPending();
    } else if (url.includes('/api/user/autoshop/history')) {
      return await mockApiService.getAutoShopHistory();
    }
    
    // For other endpoints, throw the original error
    throw error;
  }
}

export default mockApiService;
